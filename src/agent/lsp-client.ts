import { Position, Range } from 'vscode';
import { getGlobalConf, PLUGIN_ID, PLUGIN_LABEL } from '@/config';
import { homedir } from 'os';
import {
  LanguageClient,
  LanguageClientOptions,
  TextDocumentIdentifier,
  ServerOptions
} from 'vscode-languageclient/node';
import { ensureBinary, upgradeBinary } from './binary';
import { logChannel } from '@/log';
import * as cp from 'node:child_process';
import { APP_ENV } from '@/judge_env';

export type CompletionScene = 'inline' | 'panel' | 'predict';

export type CompletionParams = {
  requestId?: string;
  scene: CompletionScene;
  textDocument: TextDocumentIdentifier;
  position: Position;
  model?: string;
  choiceLimit?: number,
};

export type ListCachedCompletionsParams = {
  scene: CompletionScene;
  textDocument: TextDocumentIdentifier;
  position: Position;
};

export type CompletionAcceptedParams = {
  id: string;
  index: number;
};

export type CompletionEventParams = {
  requestId: string;
  id?: string;
  code?: string;
  time?: number;
  du?: number;
  refer?: string;
  details?: { [key: string]: any };
};

export type CompletionResultSource = 'remote' | 'cache' | 'fail-fast';

export type CompletionResponse = {
  id: string;
  requestId: string;
  choices: Choice[];
  error?: string;
  source?: CompletionResultSource;
  editableRange?: Range
  originEditable?: string
  outputExcerpt?: string
  relPath?: string
};

export type StatusVO = {
  status?: string;
};

export type Choice = {
  index: number;
  text: string;
  model?: string;
  modelLabels?: any;
};

export type DeviceRegLspVO = {
  domain: string;
  code: string;
  loginUrl: string;
};

export type AuthStatus = {
  authorized: boolean;
  domain?: string;
  account?: AccountVO;
};

export type AccountVO = {
  id?: number;
  openid: string;
  email?: string;
  nickname?: string;
};

export type AgentStatus = {
  version: string;
  status: string;
  error?: string;
  details?: { [key: string]: any };
};

export type LspConfigParams = {
  client?: ClientParams;
  platform?: PlatformParams;
  telemetry?: TelemetryConfigParams;
  copilot?: CopilotConfigParams;
};

export type TelemetryConfigParams = {
  baseUrl?: string;
};

export type CopilotConfigParams = {
  model: string;
  baseUrl?: string;
};

export type ClientParams = {
  version: String;
  channel: String;
};

export type PlatformParams = {
  family?: string;
  name?: string;
  version?: string;
  versionMajor?: string;
  lang?: string;
  timezone?: string;
  browser?: string;
  browserVersion?: string;
  browserVersionMajor?: string;
};

let lspPc: cp.ChildProcess | undefined = undefined;

export async function upgradeAndRestartLspClient(
  legacy: LanguageClient | undefined,
  version: string,
  force?: boolean
): Promise<LanguageClient> {
  console.log('Upgrading agent...', version);
  const command = await upgradeBinary(version, force);

  if (legacy) {
    console.log('Stopping legacy agent...');
    await legacy.stop();
  }

  console.log('Starting agent...');
  return await start(command);
}

export async function createLspClient(): Promise<LanguageClient> {
  const command = await ensureBinary();
  return await start(command);
}

export async function restartLspClient(): Promise<LanguageClient> {
  const command = await ensureBinary();
  if (lspPc && !lspPc.killed) {
    console.log('Stopping legacy agent...');
    lspPc.kill(9);
  }
  return start(command);
}

async function start(_command: string): Promise<LanguageClient> {
  const conf = getGlobalConf();
  const logLevel = conf.get('advance.logs.level', 'warn');

  if (_command.startsWith('~/')) {
    _command = homedir() + _command.slice('~'.length);
  }
  // TODO: 更详细的语言清单
  const clientOptions: LanguageClientOptions = {
    documentSelector: [{ scheme: 'file' }, { scheme: 'untitled' }],
    outputChannel: logChannel
  };

  console.log(
    `[${PLUGIN_LABEL}] Starting LSP server with: log level: ${logLevel}, path: ${_command}`
  );

  const childEnv = {
    ...process.env,
    CODEBUDDY_APP_ENV: APP_ENV,
  };

  const args = ['--log-level', logLevel, 'server', '--transport', 'stdio'];

  const cpm: cp.ChildProcess = cp.execFile(_command, args, { env: childEnv});
  lspPc = cpm;
  const client = new LanguageClient(
    PLUGIN_ID,
    PLUGIN_LABEL,
    () =>  new Promise<cp.ChildProcess>(resolve => resolve(cpm)),
    clientOptions
  );

  try {
    await client.start();
    console.warn(`[${PLUGIN_LABEL}] Started LSP server `);
  } catch (e) {
    console.warn(`[${PLUGIN_LABEL}] Cannot start LSP server: ${e}`);
    throw e;
  }
  return client;
}
