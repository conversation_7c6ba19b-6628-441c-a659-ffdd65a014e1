import * as vscode from 'vscode';
import { createPatch, diffLines } from 'diff';

const diffDecorationType = vscode.window.createTextEditorDecorationType({});
const originDecorationType = vscode.window.createTextEditorDecorationType({
    backgroundColor: 'rgba(255,0,0,0.3)', // 红色背景
});

export function cleanCodeDiffDecorations(activeEditor: vscode.TextEditor) {
    activeEditor.setDecorations(diffDecorationType, []);
    activeEditor.setDecorations(originDecorationType, []);
}

export function decorateCodeDiff(activeEditor: vscode.TextEditor, codeRange: vscode.Range, codeDiff: string) {
    if (!activeEditor) {
        return;
    }
    cleanCodeDiffDecorations(activeEditor);
    const maxColumn = getMaxColumnInRange(activeEditor.document, codeRange);
    const diffDecorations: vscode.DecorationOptions[] = [];
    diffDecorations.push({
        range: new vscode.Range(
            new vscode.Position(codeRange.start.line, maxColumn + 1),
            new vscode.Position(codeRange.start.line, maxColumn + 1)
        ),
        hoverMessage: new vscode.MarkdownString(
            codeDiff,
        ),
        renderOptions: {
            dark: {
                after: {
                    contentText: ' 鼠标浮动到此处查看修改建议',
                },
            },
            light: {
                after: {
                    contentText: ' 鼠标浮动到此处查看修改建议',
                },
            }
        }
    });
    const origins: vscode.DecorationOptions[] = [];
    origins.push({
        range: codeRange,
    });
    activeEditor.setDecorations(originDecorationType, origins);
    activeEditor.setDecorations(diffDecorationType, diffDecorations);
}

export function getMaxColumnInRange(document: vscode.TextDocument, range: vscode.Range): number {
    if (range.isEmpty) {
        return range.start.character;
    }
    let maxColumn = 0;
    for (let i = range.start.line; i <= range.end.line; i++) {
        const line = document.lineAt(i);
        let currentLineEffectiveEndColumn = line.range.end.character;
        if (currentLineEffectiveEndColumn > maxColumn) {
            maxColumn = currentLineEffectiveEndColumn;
        }
    }
    return maxColumn;
}

function extractLinesAfterAtAt(diffText: string): string {
    const lines = diffText.split('\n');

    // 找到第一个以 "@@" 开头的行的索引
    const atAtLineIndex = lines.findIndex(line => line.trim().startsWith('@@'));
    if (atAtLineIndex === -1) {
        // 如果没有找到 @@ 行
        return '';
    }
    // 返回 @@ 行之后的所有行
    return lines.slice(atAtLineIndex + 1).join('\n');
}

export function genPatchDiff(oldText: string, newText: string): string {
    oldText = oldText || '';
    newText = newText || '';
    const patchFileName = 'tmp.txt';
    let pp = createPatch(
        patchFileName,
        oldText,
        newText,
        'v1',
        'v2',
        { context: 30 });
    pp = extractLinesAfterAtAt(pp);
    const codediff = '```diff\n' + pp + '\n' + '```';
    return codediff;
}

export function countCommonPrefixSuffixLines(oldText: string, newText: string) {
    const diff = diffLines(oldText, newText);
    let commonPrefixLines = 0;
    let commonSuffixLines = 0;
    if (!diff[0]?.added && !diff[0]?.removed) {
        commonPrefixLines = diff[0]?.count || 0;
    }
    const diffMaxIndex = diff.length - 1;
    if (!diff[diffMaxIndex]?.added && !diff[diffMaxIndex]?.removed) {
        commonSuffixLines = diff[diffMaxIndex]?.count || 0;
    }
    return { commonPrefixLines, commonSuffixLines };
}
