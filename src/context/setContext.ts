import * as vscode from 'vscode';
import { Disposable } from 'vscode';
import { onConfigChanged, getCopilotConf } from '@/config';

/**
 * 初始化 VS Code 上下文管理
 * 设置各种 context keys 用于控制命令和菜单的可见性
 */
export function initContextManager(): Disposable[] {
  // 初始化时设置所有 context
  updateAllContexts();

  // 监听配置变化
  const configDisposable = onConfigChanged(() => {
    updateAllContexts();
  });

  return [configDisposable];
}

/**
 * 更新所有 context keys
 */
function updateAllContexts() {
  updateInlineChatContext();
  updateBlockEditContext();
}

/**
 * 更新内联聊天相关的 context
 */
function updateInlineChatContext() {
  const config = getCopilotConf();
  const inlineChatEnabled = config.get<boolean>('inlineChat.enabled', false);

  vscode.commands.executeCommand(
    'setContext',
    'li.codebuddy.copilot.inlineChat.enabled',
    !!inlineChatEnabled
  );
}

/**
 * 更新块编辑相关的 context
 */
function updateBlockEditContext() {
  const config = getCopilotConf();
  const blockEditEnabled = config.get<boolean>('blockEdit.enabled', false);

  vscode.commands.executeCommand(
    'setContext',
    'li.codebuddy.copilot.blockEdit.enabled',
    !!blockEditEnabled
  );
}
