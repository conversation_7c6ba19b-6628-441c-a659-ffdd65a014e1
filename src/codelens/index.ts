import * as vscode from 'vscode';
import { commands, languages } from 'vscode';
import { CMD_TEST_GENERATE } from '@/testing';
import { CMD_CODELENS_RUN, CMD_CODELENS_OPTIONS } from './command';
import { showOptions } from './options';

export interface CodeLensCommandContext {
  range: vscode.Range;
  symbolName: string;
}

interface CodeLensCommandArgs {
  cmd: string;
  obj: CodeLensCommandContext;
  id?: string;
}


const ACTIONS = [
  {
    command: CMD_CODELENS_OPTIONS,
    title: 'CodeBuddy'
  },
  {
    command: CMD_TEST_GENERATE,
    title: '单元测试'
  }
];

export async function initCodeLens(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
  return [
    commands.registerCommand(CMD_CODELENS_RUN, runCodeLensCommand),
    languages.registerCodeLensProvider('*', new CodeLensProviderImpl()),
    commands.registerCommand(CMD_CODELENS_OPTIONS, showOptions)
  ];
}

function runCodeLensCommand(params: CodeLensCommandArgs) {
  if (!params) {
    return;
  }
  const { cmd, obj } = params;
  commands.executeCommand(cmd, obj);
}


class CodeLensProviderImpl implements vscode.CodeLensProvider {
  async provideCodeLenses(
    doc: vscode.TextDocument,
    _token: vscode.CancellationToken
  ): Promise<vscode.CodeLens[]> {
    return await this.fromVsCode(doc);
  }

  private isSymbolSupported(kind: vscode.SymbolKind): boolean {
    switch (kind) {
      case vscode.SymbolKind.Function:
      case vscode.SymbolKind.Method:
        return true;
      default:
        return false;
    }
  }

  private async fromVsCode(
    doc: vscode.TextDocument
  ): Promise<vscode.CodeLens[]> {
    const symbols: vscode.SymbolInformation[] | undefined =
      await commands.executeCommand(
        'vscode.executeDocumentSymbolProvider',
        doc.uri
      );
    if (!symbols) {
      return [];
    }

    const shownLines: { [key: number]: boolean } = {};
    return symbols
      .filter(symbol => this.isSymbolSupported(symbol.kind))
      .filter(s => {
        const range = s.location.range;
        const start = range.start.line;
        const end = range.end.line;
        if (shownLines[end]) {
          return false;
        }
        if (end - start <= 1) {
          return false;
        }
        shownLines[end] = true;
        return true;
      })
      .flatMap(symbol =>
        ACTIONS.map(
          action =>
            new vscode.CodeLens(symbol.location.range, {
              title: action.title,
              command: CMD_CODELENS_RUN,
              arguments: [
                {
                  cmd: action.command,
                  obj: {
                    range: symbol.location.range,
                    symbolName: symbol.name
                  }
                } as CodeLensCommandArgs
              ]
            })
        )
      );
  }
}
