{"id": "ahmadalli.vscode-nginx-conf", "extensionUri": {"$mid": 1, "fsPath": "/Users/<USER>/.vscode/extensions/ahmadalli.vscode-nginx-conf-0.3.5", "path": "/Users/<USER>/.vscode/extensions/ahmadalli.vscode-nginx-conf-0.3.5", "scheme": "file"}, "extensionPath": "/Users/<USER>/.vscode/extensions/ahmadalli.vscode-nginx-conf-0.3.5", "packageJSON": {"id": "ahmadalli.vscode-nginx-conf", "identifier": {"value": "ahmadalli.vscode-nginx-conf", "_lower": "ahmadalli.vscode-nginx-conf"}, "isBuiltin": false, "isUserBuiltin": false, "isUnderDevelopment": false, "extensionLocation": {"$mid": 1, "fsPath": "/Users/<USER>/.vscode/extensions/ahmadalli.vscode-nginx-conf-0.3.5", "path": "/Users/<USER>/.vscode/extensions/ahmadalli.vscode-nginx-conf-0.3.5", "scheme": "file"}, "uuid": "9a97436d-76aa-479c-8ae9-db2f400a7b04", "targetPlatform": "undefined", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "preRelease": false, "name": "vscode-nginx-conf", "displayName": "NGINX Configuration Language Support", "description": "Nginx configuration helper for Visual Studio Code with many features including syntax highlight, auto-complete, in-editor documents, embedded Lua block, and more", "version": "0.3.5", "license": "GPL-3.0", "icon": "images/icon.png", "galleryBanner": {"color": "#E5FFEC", "theme": "light"}, "categories": ["Snippets", "Formatters", "Programming Languages"], "keywords": ["<PERSON><PERSON><PERSON>", "OpenResty", "Configuration", "Auto-completion"], "publisher": "<PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "contributors": [{"name": "<PERSON>", "url": "https://github.com/AdrianDsg"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/halilim"}, {"name": "tiansin", "url": "https://github.com/tiansin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/latipun7"}], "main": "./out/extension/main.desktop.js", "browser": "./out/extension/main.web.js", "scripts": {"build": "node scripts/run clean build:ts build:syntax build:web", "build:dev": "node scripts/run build:ts build:syntax", "build:ts": "swc -d out src --strip-leading-paths --copy-files", "build:web": "webpack --mode production --devtool hidden-source-map", "build:syntax": "node --enable-source-maps out/syntax/generate-tmLanguage", "lint:types": "tsc", "lint:eslint": "eslint --ext .js,.ts src test", "clean": "node scripts/clean"}, "extensionKind": ["ui", "workspace"], "extensionDependencies": [], "dependencies": {}, "devDependencies": {"@swc/cli": "^0.3.10", "@swc/core": "^1.4.8", "@types/node": "^20.11.28", "@types/turndown": "^5.0.4", "@types/vscode": "^1.64.0", "axios": "^1", "cheerio": "1.0.0-rc.12", "html-minifier": "^4.0.0", "https-proxy-agent": "^7", "process": "^0.11.10", "rimraf": "^5", "swc-loader": "^0.2.6", "turndown": "^7.1.2", "typescript": "^5", "webpack": "^5", "webpack-cli": "^5"}, "optionalDependencies": {"@typescript-eslint/eslint-plugin": "^7", "@typescript-eslint/parser": "^7", "eslint": "^8", "vsce": "^2"}, "engines": {"vscode": "^1.64.0"}, "activationEvents": ["onLanguage:nginx", "onLanguage:lua"], "contributes": {"languages": [{"id": "nginx", "aliases": ["NGINX Conf", "NGINX", "nginx"], "extensions": [".conf", ".conf.default", ".conf.template", "mime.types", "fastcgi_params", "scgi_params", "uwsgi_params", "nginx.conf", ".nginx"], "configuration": "./nginx.configuration.json"}], "snippets": [{"language": "nginx", "path": "./assets/snippets/nginx.json"}, {"language": "lua", "path": "./assets/snippets/lua.json"}], "grammars": [{"language": "nginx", "scopeName": "source.nginx", "path": "./out/syntax/nginx.tmLanguage", "embeddedLanguages": {"meta.embedded.block.lua": "lua"}}], "commands": [{"command": "nginx-conf-hint.showDocument", "title": "Goto Nginx Document", "shortTitle": "NginxDocs", "category": "<PERSON><PERSON><PERSON>"}], "configuration": [{"title": "Nginx Configuration", "properties": {"nginx-conf-hint.enableStrictCompletion": {"title": "Strict Completion", "type": "boolean", "default": true, "description": "If `true`, only auto-complete directives that can be used in current context"}, "nginx-conf-hint.format.align": {"title": "Alignment", "type": "boolean", "default": false, "description": "Set whether to perform alignment formatting"}, "nginx-conf-hint.externalModules": {"title": "Nginx External Modules", "type": "array", "items": {"enum": ["lua", "js"]}, "default": [], "description": "Enabled hint data for external modules (lua,js)"}}}], "menus": {"editor/context": [{"when": "!inOutput && resourceLangId == 'nginx'", "command": "nginx-conf-hint.showDocument", "group": "navigation"}]}}, "repository": {"type": "git", "url": "https://github.com/ahmadalli/vscode-nginx-conf"}, "bugs": {"url": "https://github.com/ahmadalli/vscode-nginx-conf/issues"}, "homepage": "https://github.com/ahmadalli/vscode-nginx-conf"}, "extensionKind": 1, "isFromDifferentExtensionHost": false}