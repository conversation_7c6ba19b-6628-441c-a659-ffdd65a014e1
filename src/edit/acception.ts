import * as vscode from 'vscode';
import { cleanCodeDiffDecorations } from '@/utils/vscode/text-editor-decoration';

export type CurrentShownEditData = {
    editor: vscode.TextEditor | undefined,
    range: vscode.Range | undefined,
    text: string,
};


export const currentShownEdit: CurrentShownEditData = {
    editor: undefined,
    range: new vscode.Range(0, 0, 0, 0),
    text: '',
};

export function setCurrentShownEdit(editor: vscode.TextEditor | undefined, range: vscode.Range | undefined, text: string) {
    currentShownEdit.editor = editor;
    currentShownEdit.range = range;
    currentShownEdit.text = text;
}

export function acceptEdit() {
    console.log('here we go, edit accept');
    if (!currentShownEdit.editor || !currentShownEdit.range) {
        return;
    }
    const editor = currentShownEdit.editor;
    editor.edit(editBuilder => {
        if (!currentShownEdit.range) { return; }
        // The replace method handles both insertion (if range is empty)
        // and replacement (if range is not empty).
        editBuilder.replace(currentShownEdit.range, currentShownEdit.text);
    });
    cleanCodeDiffDecorations(editor);
    setCurrentShownEdit(undefined, undefined, '');
}
