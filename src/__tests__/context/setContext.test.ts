import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import * as vscode from 'vscode';
import { Disposable } from 'vscode';
import { 
  initContextManager, 
  updateAllContexts, 
  updateInlineChatContext, 
  updateBlockEditContext 
} from '../../context/setContext';
import { onConfigChanged, getCopilotConf } from '@/config';

// Mock vscode module
vi.mock('vscode', () => ({
  commands: {
    executeCommand: vi.fn()
  },
  Disposable: vi.fn()
}));

// Mock config module
vi.mock('@/config', () => ({
  onConfigChanged: vi.fn(),
  getCopilotConf: vi.fn()
}));

describe('setContext', () => {
  let mockExecuteCommand: Mock;
  let mockOnConfigChanged: Mock;
  let mockGetCopilotConf: Mock;
  let mockConfigGet: Mock;
  let mockDisposable: Disposable;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup vscode mocks
    mockExecuteCommand = vi.mocked(vscode.commands.executeCommand);
    
    // Setup config mocks
    mockOnConfigChanged = vi.mocked(onConfigChanged);
    mockGetCopilotConf = vi.mocked(getCopilotConf);
    
    // Setup config.get mock
    mockConfigGet = vi.fn();
    
    // Setup disposable mock
    mockDisposable = { dispose: vi.fn() } as Disposable;
    mockOnConfigChanged.mockReturnValue(mockDisposable);
    
    // Setup getCopilotConf to return an object with get method
    mockGetCopilotConf.mockReturnValue({
      get: mockConfigGet
    } as any);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('initContextManager', () => {
    it('should initialize context manager and return disposables', () => {
      const disposables = initContextManager();
      
      // Should call updateAllContexts initially
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2); // Called by both update functions
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2); // Called by both update functions
      
      // Should setup config change listener
      expect(mockOnConfigChanged).toHaveBeenCalledTimes(1);
      expect(mockOnConfigChanged).toHaveBeenCalledWith(expect.any(Function));
      
      // Should return array with disposable
      expect(disposables).toEqual([mockDisposable]);
    });

    it('should call updateAllContexts when config changes', () => {
      initContextManager();
      
      // Clear previous calls
      vi.clearAllMocks();
      mockGetCopilotConf.mockReturnValue({
        get: mockConfigGet
      } as any);
      
      // Get the callback function passed to onConfigChanged
      const configChangeCallback = mockOnConfigChanged.mock.calls[0][0];
      
      // Call the callback to simulate config change
      configChangeCallback();
      
      // Should call updateAllContexts again
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2);
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateAllContexts', () => {
    it('should call both updateInlineChatContext and updateBlockEditContext', () => {
      updateAllContexts();
      
      // Should call getCopilotConf twice (once for each update function)
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(2);
      
      // Should call executeCommand twice (once for each context)
      expect(mockExecuteCommand).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateInlineChatContext', () => {
    it('should set inline chat context to true when enabled', () => {
      mockConfigGet.mockReturnValue(true);
      
      updateInlineChatContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        true
      );
    });

    it('should set inline chat context to false when disabled', () => {
      mockConfigGet.mockReturnValue(false);
      
      updateInlineChatContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        false
      );
    });

    it('should set inline chat context to false when config returns undefined', () => {
      mockConfigGet.mockReturnValue(undefined);
      
      updateInlineChatContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('inlineChat.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        false
      );
    });

    it('should convert truthy values to true using double negation', () => {
      mockConfigGet.mockReturnValue('some string'); // truthy value
      
      updateInlineChatContext();
      
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.inlineChat.enabled',
        true
      );
    });
  });

  describe('updateBlockEditContext', () => {
    it('should set block edit context to true when enabled', () => {
      mockConfigGet.mockReturnValue(true);
      
      updateBlockEditContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('blockEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.blockEdit.enabled',
        true
      );
    });

    it('should set block edit context to false when disabled', () => {
      mockConfigGet.mockReturnValue(false);
      
      updateBlockEditContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('blockEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.blockEdit.enabled',
        false
      );
    });

    it('should set block edit context to false when config returns undefined', () => {
      mockConfigGet.mockReturnValue(undefined);
      
      updateBlockEditContext();
      
      expect(mockGetCopilotConf).toHaveBeenCalledTimes(1);
      expect(mockConfigGet).toHaveBeenCalledWith('blockEdit.enabled', false);
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.blockEdit.enabled',
        false
      );
    });

    it('should convert truthy values to true using double negation', () => {
      mockConfigGet.mockReturnValue(1); // truthy value
      
      updateBlockEditContext();
      
      expect(mockExecuteCommand).toHaveBeenCalledWith(
        'setContext',
        'li.codebuddy.copilot.blockEdit.enabled',
        true
      );
    });
  });

  describe('error handling', () => {
    it('should handle errors when executeCommand throws', () => {
      mockExecuteCommand.mockRejectedValue(new Error('Command failed'));
      mockConfigGet.mockReturnValue(true);
      
      // Should not throw
      expect(() => updateInlineChatContext()).not.toThrow();
      expect(() => updateBlockEditContext()).not.toThrow();
    });

    it('should handle errors when getCopilotConf throws', () => {
      mockGetCopilotConf.mockImplementation(() => {
        throw new Error('Config failed');
      });
      
      // Should throw since we don't handle this error in the implementation
      expect(() => updateInlineChatContext()).toThrow('Config failed');
      expect(() => updateBlockEditContext()).toThrow('Config failed');
    });
  });
});
